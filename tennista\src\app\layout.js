// src/app/layout.tsx
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/Navbar";
import Link from "next/link";
import type { ReactNode } from "react";
import { supabaseServer } from "@/lib/supabaseServer";

const geistSans = Geist({ variable: "--font-geist-sans", subsets: ["latin"] });
const geistMono = Geist_Mono({ variable: "--font-geist-mono", subsets: ["latin"] });

export const metadata = {
  title: "Tennista",
  description: "Tennis analytics dashboard",
};

export default async function RootLayout({ children }: { children: ReactNode }) {
  // 服务器端获取当前用户（无会话则为 null）
  const supabase = supabaseServer();
  let user: any = null;
  try {
    const { data, error } = await supabase.auth.getUser();
    if (!error) user = data.user;
  } catch {
    // 静默处理：没有会话或环境问题时 user 维持为 null
  }

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen
        bg-gradient-to-br from-purple-200 to-indigo-100`}
      >
        {/* Header */}
        <header className="sticky top-0 z-10 bg-white/70 backdrop-blur border-b">
          <div className="mx-auto max-w-6xl px-4 h-14 flex items-center">
            {/* 左侧品牌 */}
            <Link href="/" className="flex items-center gap-2 mr-6 shrink-0">
              <span className="text-2xl">🎾</span>
              <span className="font-semibold">Tennista</span>
            </Link>

            {/* 中间导航 */}
            <div className="flex-1">
              <Navbar />
            </div>

            {/* 右侧：未登录 → Sign in；已登录 → Welcome, xxx */}
            {user ? (
              <Link
                href="/user"
                className="ml-6 text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
              >
                {`Welcome, ${user.user_metadata?.username ?? user.email ?? "User"}`}
              </Link>
            ) : (
              <Link
                href="/auth/login"
                className="ml-6 text-sm text-slate-700 hover:text-slate-900 hover:underline shrink-0"
              >
                Sign in
              </Link>
            )}
          </div>
        </header>

        {/* Page content */}
        <main className="mx-auto max-w-6xl px-4 py-6">{children}</main>
      </body>
    </html>
  );
}
